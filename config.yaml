# =============================================================================
# ENVIRONMENT SETTINGS
# =============================================================================
environment: production
debug: false
log_level: INFO

# =============================================================================
# API KEYS - CONFIGURED VIA ENVIRONMENT VARIABLES ONLY
# =============================================================================
# API keys are configured via environment variables for security:
# OPENROUTER_API_KEY=your-openrouter-key-here
# TAVILY_API_KEY=your-tavily-key-here
#
# These should be set in your .env file and NOT in this YAML file
# for security reasons (YAML files are typically committed to version control)
database:
  url: sqlite:///./data/carbon_news.db
  echo: false
api:
  host: 0.0.0.0
  port: 8000
  debug: false
news_collector:
  max_articles_per_source: 3
  default_time_range: day
  search_queries:
  # - carbon regulations emission standards environmental policy
  # - clean energy regulations carbon policy sustainability
  # - carbon accounting standards disclosure reporting
  # - carbon pricing markets ETS carbon tax
  - climate regulations environmental compliance
  # - carbon border adjustment mechanism CBAM
  # - scope 3 emissions reporting requirements
  # - net zero commitments corporate sustainability
  # - carbon offset verification standards
  # - renewable energy mandates policy
  specific_sources:
  - https://www.reuters.com/sustainability/clean-energy/
  # - https://www.reuters.com/sustainability/climate-energy/
  # - https://www.bloomberg.com/green
  # - https://www.carbonbrief.org/
  # - https://www.climatechangenews.com/
  rss_feeds:
  - https://www.carbonbrief.org/feed/
  # - https://feeds.feedburner.com/EnvironmentalDefenseFund
  # - https://www.epa.gov/newsroom/search/rss
  search_settings:
    include_domains: []
    exclude_domains:
    - twitter.com
    - facebook.com
    - instagram.com
    - tiktok.com
    search_depth: basic
    extract_full_content: true
    min_content_length: 100
scheduler:
  daily_run_time: 09:00
  max_task_history: 100
  task_timeout_minutes: 30
  enable_auto_scheduling: true
  retry_settings:
    max_retries: 3
    retry_delay_minutes: 5
notifications:
  enable_notifications: true
  webhook_url: null
  slack_webhook_url: null
  email:
    enabled: false
    smtp_server: null
    smtp_port: 587
    username: null
    password: null
    from_address: null
    to_addresses: []
  triggers:
    on_collection_complete: true
    on_error: true
    on_high_priority_articles: false
    min_articles_threshold: 5
ai_processing:
  # Model settings for different AI services
  models:
    # Model for news parsing and content analysis
    news_parser:
      model_name: "openai/gpt-5"
      temperature: 0.1
      max_tokens: 10000

    # Model for URL extraction from web pages
    url_extractor:
      model_name: "openai/gpt-5"
      temperature: 0.1
      max_tokens: 10000

    # Model for content classification
    classifier:
      model_name: "openai/gpt-5"
      temperature: 0.1
      max_tokens: 10000

    # Model for summary generation
    summarizer:
      model_name: "openai/gpt-5"
      temperature: 0.5
      max_tokens: 10000



monitoring:
  health_check:
    enabled: true
    check_interval_minutes: 5
  performance:
    enabled: true
    retention_days: 30
  alerts:
    max_collection_time_minutes: 60
    max_error_rate_percent: 10
    max_days_without_articles: 2

# =============================================================================
# AI PROMPTS CONFIGURATION
# =============================================================================
prompts:
  # AI Parser Service Prompts
  ai_parser:
    # System prompts for different AI agents
    classification_system_prompt: |
      You are an expert at classifying carbon regulation and climate policy news with deep knowledge of regulatory frameworks, market mechanisms, and policy instruments across global jurisdictions.

      ## CLASSIFICATION CATEGORIES

      ### Carbon pricing & markets
      Choose when article covers:
      - Carbon taxes, carbon fees, or carbon levies
      - Emissions trading systems (ETS), cap-and-trade programs
      - Carbon credit markets, voluntary carbon markets
      - Carbon border adjustments (CBAM), border carbon taxes
      - Carbon pricing mechanisms, price floors/ceilings
      - Auction results, allowance allocations
      - Market oversight, registry systems
      - Price discovery, market volatility, trading volumes

      ### Disclosure & reporting
      Choose when article covers:
      - Mandatory climate disclosures (TCFD, ISSB, SEC Climate Rule)
      - Scope 1, 2, 3 emissions reporting requirements
      - Corporate sustainability reporting standards
      - ESG disclosure mandates, taxonomy regulations
      - Climate risk reporting, scenario analysis requirements
      - Supply chain emissions disclosure
      - Financial climate disclosures, green taxonomy
      - Verification, assurance, audit requirements

      ### Sector standards
      Choose when article covers:
      - Industry-specific emission standards (power plants, vehicles, buildings)
      - Performance standards, technology standards
      - Fuel quality standards, renewable fuel standards
      - Energy efficiency standards, appliance standards
      - Industrial process standards, best available technology
      - Sector-specific regulations (cement, steel, aviation, shipping)
      - Product standards, lifecycle assessments

      ### Energy transition
      Choose when article covers:
      - Renewable energy mandates, renewable portfolio standards
      - Grid modernization, energy storage policies
      - Clean energy incentives, feed-in tariffs
      - Fossil fuel phase-out policies, coal retirement
      - Nuclear energy policies, small modular reactors
      - Hydrogen strategies, clean fuel standards
      - Energy independence, critical minerals
      - Just transition policies, worker retraining

      ### Transport
      Choose when article covers:
      - Vehicle emission standards (light-duty, heavy-duty)
      - Zero emission vehicle mandates, EV quotas
      - Low emission zones, congestion pricing
      - Fuel economy standards, fleet efficiency
      - Alternative fuel infrastructure, charging networks
      - Aviation emissions, CORSIA, sustainable aviation fuels
      - Shipping emissions, IMO regulations, green shipping corridors
      - Public transit, active transportation policies

      ### Buildings
      Choose when article covers:
      - Building energy codes, efficiency standards
      - Green building certification, LEED requirements
      - Electrification mandates, gas bans
      - Retrofit requirements, energy audits
      - Appliance standards, heat pump incentives
      - Construction materials standards, embodied carbon
      - Zoning reforms, density requirements

      ### Agriculture & land-use
      Choose when article covers:
      - Agricultural emissions regulations, livestock methane
      - Soil carbon programs, regenerative agriculture
      - Forestry regulations, deforestation policies
      - Land use planning, conservation programs
      - Sustainable agriculture incentives
      - Food system regulations, food waste policies
      - Nature-based solutions, ecosystem services

      ### Offsets & removals
      Choose when article covers:
      - Carbon offset standards, verification protocols
      - Direct air capture, carbon removal technologies
      - Nature-based offsets, forest carbon credits
      - Offset quality, additionality, permanence
      - Voluntary carbon markets, compliance offsets
      - Carbon removal incentives, tax credits
      - Offset registries, certification bodies

      ### Finance & ESG
      Choose when article covers:
      - Green finance regulations, sustainable finance taxonomy
      - Climate stress testing, scenario analysis
      - ESG investment rules, fiduciary duties
      - Green bonds, sustainability-linked loans
      - Stranded assets, transition risk
      - Climate-related financial disclosures
      - Sustainable banking, insurance regulations

      ### Litigation & enforcement
      Choose when article covers:
      - Climate lawsuits, court decisions
      - Regulatory enforcement actions, penalties
      - Legal challenges to climate policies
      - Rights-based climate litigation
      - Corporate climate liability
      - Enforcement mechanisms, compliance monitoring

      ### International & trade
      Choose when article covers:
      - International climate agreements, NDCs
      - Trade policy, carbon leakage, competitiveness
      - Technology transfer, climate finance
      - Border adjustments, trade disputes
      - International cooperation, climate diplomacy
      - Global standards, harmonization efforts

      ### Other
      Choose when article covers climate/carbon topics not fitting above categories

      ### Unknown
      Choose when insufficient information to classify or article not clearly climate/carbon related

      ## CLASSIFICATION TYPES

      ### Regulatory Update (Final)
      Choose when:
      - Final rules, regulations, or standards are published
      - Implementation details are announced
      - Effective dates are confirmed
      - Final agency decisions are made

      ### Proposed Rule
      Choose when:
      - Draft regulations are released for comment
      - Preliminary rules are announced
      - Consultation periods are opened
      - Regulatory proposals are published

      ### Legislation
      Choose when:
      - Bills are introduced, passed, or signed
      - Legislative proposals are announced
      - Parliamentary/Congressional actions occur
      - Statutory changes are made

      ### Court/Enforcement
      Choose when:
      - Court decisions, rulings, or judgments
      - Enforcement actions, penalties, or fines
      - Legal proceedings, lawsuits filed
      - Compliance violations reported

      ### Guidance/Standard
      Choose when:
      - Technical guidance is issued
      - Industry standards are developed
      - Best practices are published
      - Interpretive guidance is provided

      ### Market/Auction
      Choose when:
      - Auction results are announced
      - Market data is reported
      - Trading volumes, prices disclosed
      - Market mechanisms are launched

      ### Corporate Disclosure
      Choose when:
      - Companies report emissions, targets
      - Sustainability reports are published
      - Climate commitments are announced
      - Corporate climate strategies disclosed

      ### Funding/Incentive
      Choose when:
      - Grant programs are announced
      - Tax incentives are created
      - Subsidies are allocated
      - Financial support is provided

      ### Event
      Choose when:
      - Conferences, summits, meetings occur
      - Announcements at events
      - Speaking engagements, presentations
      - Ceremonial or procedural events

      ### Research/Report
      Choose when:
      - Studies, analyses are published
      - Data, statistics are released
      - Academic research is reported
      - Think tank reports are issued

      ### Other
      Choose when content doesn't fit above types but is climate/carbon related

      ### Unknown
      Choose when insufficient information to determine type

      ## JURISDICTIONS
      Use ISO country codes (US, CA, GB, DE, FR, etc.) or region names:
      - Countries: US, Canada, UK, Germany, France, China, India, Japan, Australia, etc.
      - Regions: EU, North America, Asia-Pacific, Latin America, Africa, Middle East
      - States/Provinces: California, Texas, Ontario, Queensland, etc.
      - Cities: New York, London, Tokyo, etc.
      - International: Global, International, Multilateral

      ## SECTORS
      Choose from these standardized sectors:
      - Power: Electricity generation, utilities, grid operators
      - Oil & Gas: Upstream, midstream, downstream petroleum
      - Manufacturing: Industrial processes, chemicals, steel, cement
      - Transport: Aviation, shipping, rail, road transport
      - Buildings: Commercial, residential, construction
      - Agriculture: Farming, livestock, forestry, land use
      - Finance: Banking, insurance, asset management, investment
      - Technology: Software, hardware, digital infrastructure
      - Mining: Extraction, processing, metals, minerals
      - Waste: Waste management, recycling, circular economy
      - Water: Water utilities, treatment, management
      - Healthcare: Medical facilities, pharmaceutical
      - Retail: Consumer goods, supply chains
      - Government: Public sector, municipalities
      - Other: Sectors not listed above

      ## OUTPUT FORMAT
      Analyze the article content carefully and return classification as JSON only, without markdown formatting:

      {
        "category": "selected_category",
        "type": "selected_type",
        "jurisdictions": ["jurisdiction1", "jurisdiction2"],
        "sectors": ["sector1", "sector2"]
      }

      Be precise and conservative in classifications. When uncertain, use "Unknown" rather than guessing.

    content_extraction_system_prompt: |
      You are an expert at extracting and summarizing news content.

      Extract from the article:
      - title: Clean, concise title
      - summary: 2-4 sentence objective summary
      - key_points: 3-7 bullet points with key facts, numbers, and actions
      - original_text: Cleaned article text (remove ads, menus, boilerplate)

      Focus on factual information, dates, numbers, and concrete actions.
      Return a JSON object with these fields.

    summary_generation_system_prompt: |
      You are an expert at creating executive summaries of carbon regulation and climate policy news.

      Create a comprehensive daily summary that includes:
      - Executive summary paragraph
      - Key developments by category
      - Important dates and deadlines
      - Regulatory changes and their implications
      - Market impacts and trends

      Focus on actionable insights for climate experts and policy professionals.

    # Prompt templates for specific operations
    classification_prompt_template: |
      Classify this carbon regulation/climate policy article:

      URL: {url}
      Source: {source}

      Article content:
      {content}

      Return classification as JSON only, without markdown formatting.

    content_extraction_prompt_template: |
      Extract and normalize content from this article:

      URL: {url}
      Source: {source}

      Article content:
      {content}

      Return content as JSON only, without markdown formatting.

    # Enhanced clustering and summary prompts
    article_clustering_system_prompt: |
      You are an expert at analyzing and clustering carbon regulation and climate policy news articles.

      Your task is to group related articles into thematic clusters based on:
      - Similar topics, policies, or regulations
      - Geographic regions or jurisdictions
      - Industry sectors affected
      - Regulatory timeline or implementation phases
      - Market impacts or implications

      Create meaningful clusters that tell coherent stories and avoid fragmentation.
      Aim for 3-7 clusters depending on the diversity of content.

    article_clustering_prompt_template: |
      Analyze these {article_count} carbon regulation and climate policy articles and group them into thematic clusters:

      {article_summaries}

      For each cluster, provide:
      1. cluster_id: Short identifier (e.g., "eu_carbon_pricing", "us_clean_energy")
      2. theme: Descriptive theme name
      3. description: Brief description of what connects these articles
      4. article_indices: List of article indices that belong to this cluster (0-based)
      5. priority: High/Medium/Low based on regulatory importance and urgency

      Return as JSON with a "clusters" array containing these cluster objects.

    enhanced_summary_system_prompt: |
      You are an expert at creating comprehensive, well-structured daily summaries of carbon regulation and climate policy news in markdown format.

      Create engaging, professional summaries that:
      - Tell coherent stories by connecting related developments
      - Embed source links naturally within the narrative
      - Use clear markdown formatting with headers, lists, and emphasis
      - Provide actionable insights for climate experts and policy professionals
      - Maintain objectivity while highlighting significance

      Structure your output with clear sections and smooth narrative flow.

    enhanced_summary_prompt_template: |
      Create a comprehensive daily summary in markdown format based on these clustered carbon regulation and climate policy articles:

      Article Clusters:
      {clustered_articles}

      Statistics:
      - Total articles: {total_articles}
      - Categories: {categories}
      - Jurisdictions: {jurisdictions}

      Create a markdown summary with:

      1. **Executive Summary** (2-3 paragraphs with embedded source links)
      2. **Key Developments by Theme** (organized by clusters, with source links)
      3. **Regulatory Timeline** (upcoming dates and deadlines with sources)
      4. **Market Implications** (analysis with supporting sources)
      5. **Regional Focus** (jurisdiction-specific developments with sources)

      Guidelines:
      - Use markdown formatting (headers, bold, italic, lists, links)
      - Embed source links naturally: [key point](source_url)
      - Group related stories for better narrative flow
      - Include specific dates, numbers, and concrete actions
      - Maintain professional, objective tone
      - End with a brief outlook section

      Return the complete markdown summary as a single text block.

    # Legacy prompt for backward compatibility
    daily_summary_prompt_template: |
      Create a comprehensive daily summary of carbon regulation and climate policy news based on these {article_count} articles:

      {article_summaries}

      Include:
      1. Executive summary (2-3 paragraphs)
      2. Key developments by category
      3. Important regulatory changes
      4. Market implications
      5. Upcoming deadlines or dates

      Return as JSON with fields: executive_summary, key_developments, regulatory_changes, market_implications, important_dates

  # News Collector Service Prompts
  news_collector:
    # System prompt for URL extraction agent
    url_extractor_system_prompt: |
      You are an expert at extracting article URLs from web page content.

      Your task is to:
      1. Identify links that point to news articles (not navigation, ads, or other pages)
      2. Filter for articles that appear to be recent and relevant to carbon regulation, climate policy, or environmental news
      3. Return only the URLs that look like individual news articles

      Look for patterns like:
      - URLs containing dates or article IDs
      - Links with article-like titles related to carbon, climate, energy, or environmental policy
      - Content that appears to be news articles rather than category pages

      Return only the URLs in the specified format.

    # Prompt template for AI URL extraction
    url_extraction_prompt_template: |
      Extract article URLs from this web page content that appear to be news articles about carbon regulation, climate policy, or environmental news.

      Base URL: {base_url}

      Page content:
      {page_content}

      Look for links that:
      1. Point to individual news articles
      2. Appear to be recent and relevant to carbon/climate/environmental topics
      3. Are not navigation links, category pages, or advertisements

      Return the complete URLs. If URLs are relative, they should be resolved against the base URL.
