"""
AI-powered news parsing service for extracting structured information from news articles.
"""

import re
from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from dataclasses import dataclass
from sqlalchemy.orm import Session

from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIChatModel
from pydantic_ai.providers.openrouter import OpenRouterProvider
from pydantic_ai.settings import ModelSettings

from ..core.config import get_settings
from ..core.logging import LoggerMixin
from ..core.models import NewsArticle
from ..core.database import get_db_session
from ..models import (
    AIClassification,
    AIContent,
    ArticleClustering,
    DailySummary,
    EnhancedSummary,
    ItemType,
    Category,
    Details,
    RegulatoryUpdateDetails,
    ProposedRuleDetails,
    LegislationDetails,
    CourtEnforcementDetails,
    GuidanceStandardDetails,
    MarketAuctionDetails,
    CorporateDisclosureDetails,
    FundingIncentiveDetails,
    EventDetails,
    ResearchReportDetails,
)


@dataclass
class ExtractInput:
    """Input data for AI extraction."""
    url: str
    outlet: Optional[str]
    raw_content: str


class ModelManager:
    """Model manager for creating configured AI models."""

    _models: Dict[str, OpenAIChatModel] = {}

    @classmethod
    def get_model(cls, model_type: str = "news_parser") -> OpenAIChatModel:
        """Get a configured model instance for the specified type."""
        if model_type not in cls._models:
            settings = get_settings()
            api_key = settings.openrouter_api_key
            if not api_key:
                raise RuntimeError("Missing OPENROUTER_API_KEY in environment")

            # Get model configuration based on type
            if model_type == "news_parser":
                model_config = settings.ai_processing.models.news_parser
            elif model_type == "classifier":
                model_config = settings.ai_processing.models.classifier
            elif model_type == "summarizer":
                model_config = settings.ai_processing.models.summarizer
            else:
                # Default to news_parser config
                model_config = settings.ai_processing.models.news_parser

            cls._models[model_type] = OpenAIChatModel(
                model_config.model_name,
                provider=OpenRouterProvider(api_key=api_key),
                settings=ModelSettings(
                    temperature=model_config.temperature,
                    max_tokens=model_config.max_tokens,
                ),
            )
        return cls._models[model_type]


class AINewsParsingService(LoggerMixin):
    """Service for AI-powered parsing and analysis of news articles."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the AI parsing service."""
        self.settings = get_settings()
        self.db = db_session or get_db_session()
        self._agents = {}

        self.log_method_call("__init__")
    
    def _get_classification_agent(self) -> Agent:
        """Get or create classification agent with structured output."""
        if 'classification' not in self._agents:
            system_prompt = self.settings.prompts.ai_parser.classification_system_prompt

            model = ModelManager.get_model("classifier")

            self._agents['classification'] = Agent(
                model,
                output_type=AIClassification,
                system_prompt=system_prompt,
            )

        return self._agents['classification']

    def _get_content_agent(self) -> Agent:
        """Get or create content extraction agent with structured output."""
        if 'content' not in self._agents:
            system_prompt = self.settings.prompts.ai_parser.content_extraction_system_prompt

            model = ModelManager.get_model("news_parser")

            self._agents['content'] = Agent(
                model,
                output_type=AIContent,
                system_prompt=system_prompt,
            )

        return self._agents['content']

    def _get_summary_agent(self) -> Agent:
        """Get or create summary generation agent with structured output."""
        if 'summary' not in self._agents:
            system_prompt = self.settings.prompts.ai_parser.summary_generation_system_prompt

            model = ModelManager.get_model("summarizer")

            self._agents['summary'] = Agent(
                model,
                output_type=DailySummary,
                system_prompt=system_prompt,
            )

        return self._agents['summary']

    def _get_clustering_agent(self) -> Agent:
        """Get or create article clustering agent with structured output."""
        if 'clustering' not in self._agents:
            system_prompt = self.settings.prompts.ai_parser.article_clustering_system_prompt

            model = ModelManager.get_model("summarizer")

            self._agents['clustering'] = Agent(
                model,
                output_type=ArticleClustering,
                system_prompt=system_prompt,
            )

        return self._agents['clustering']

    def _get_enhanced_summary_agent(self) -> Agent:
        """Get or create enhanced summary generation agent with structured output."""
        if 'enhanced_summary' not in self._agents:
            system_prompt = self.settings.prompts.ai_parser.enhanced_summary_system_prompt

            model = ModelManager.get_model("summarizer")

            self._agents['enhanced_summary'] = Agent(
                model,
                output_type=EnhancedSummary,
                system_prompt=system_prompt,
            )

        return self._agents['enhanced_summary']

    def _get_detail_agent(self, item_type: ItemType) -> Optional[Agent]:
        """Get the appropriate detail extraction agent for the given item type."""
        agent_configs = {
            ItemType.REGULATORY_UPDATE: RegulatoryUpdateDetails,
            ItemType.PROPOSED_RULE: ProposedRuleDetails,
            ItemType.LEGISLATION: LegislationDetails,
            ItemType.COURT_ENFORCEMENT: CourtEnforcementDetails,
            ItemType.GUIDANCE_STANDARD: GuidanceStandardDetails,
            ItemType.MARKET_AUCTION: MarketAuctionDetails,
            ItemType.CORPORATE_DISCLOSURE: CorporateDisclosureDetails,
            ItemType.FUNDING_INCENTIVE: FundingIncentiveDetails,
            ItemType.EVENT: EventDetails,
            ItemType.RESEARCH_REPORT: ResearchReportDetails,
        }

        if item_type not in agent_configs:
            return None

        cache_key = f'detail_{item_type.value}'
        if cache_key not in self._agents:
            output_type = agent_configs[item_type]

            # Use a generic detail extraction prompt - could be made type-specific
            system_prompt = "Extract the specific details for this type of article. Return only the structured data requested."

            model = ModelManager.get_model("news_parser")

            self._agents[cache_key] = Agent(
                model,
                output_type=output_type,
                system_prompt=system_prompt,
            )

        return self._agents[cache_key]

    def _clean_json_response(self, response_text: str) -> str:
        """Clean AI response text to extract JSON from markdown code blocks."""
        # Remove markdown code blocks
        if response_text.strip().startswith('```'):
            # Find the start and end of the code block
            lines = response_text.strip().split('\n')
            start_idx = 0
            end_idx = len(lines)

            # Find start (skip ```json or ```)
            for i, line in enumerate(lines):
                if line.strip().startswith('```'):
                    start_idx = i + 1
                    break

            # Find end (look for closing ```)
            for i in range(len(lines) - 1, -1, -1):
                if lines[i].strip() == '```':
                    end_idx = i
                    break

            # Extract content between code blocks
            json_lines = lines[start_idx:end_idx]
            response_text = '\n'.join(json_lines)

        return response_text.strip()

    def _cluster_articles(self, article_summaries: List[Dict[str, Any]]) -> ArticleClustering:
        """Cluster articles by themes and topics using AI."""
        self.log_method_call("_cluster_articles", article_count=len(article_summaries))

        if len(article_summaries) <= 1:
            # No clustering needed for single article
            from ..models import ArticleCluster
            return ArticleClustering(
                clusters=[ArticleCluster(
                    cluster_id="single_article",
                    theme="Daily Update",
                    description="Single article update",
                    article_indices=[0] if article_summaries else [],
                    priority="Medium"
                )]
            )

        try:
            prompt = self.settings.prompts.ai_parser.article_clustering_prompt_template.format(
                article_count=len(article_summaries),
                article_summaries=str(article_summaries)[:6000]  # Limit content size
            )

            result = self._get_clustering_agent().run_sync(prompt)
            clustering_result = result.output

            # Ensure all articles are assigned to clusters
            assigned_indices = set()
            for cluster in clustering_result.clusters:
                assigned_indices.update(cluster.article_indices)

            # Create fallback cluster for unassigned articles
            unassigned = [i for i in range(len(article_summaries)) if i not in assigned_indices]
            if unassigned:
                from ..models import ArticleCluster
                clustering_result.clusters.append(ArticleCluster(
                    cluster_id="miscellaneous",
                    theme="Other Developments",
                    description="Additional regulatory and policy updates",
                    article_indices=unassigned,
                    priority="Low"
                ))

            self.logger.info("Articles clustered successfully",
                           cluster_count=len(clustering_result.clusters),
                           article_count=len(article_summaries))

            return clustering_result

        except Exception as e:
            self.logger.warning("Article clustering failed, using fallback", error=str(e))
            # Fallback: create clusters by category
            category_clusters = {}
            for i, article in enumerate(article_summaries):
                category = article.get("category", "Unknown")
                if category not in category_clusters:
                    category_clusters[category] = []
                category_clusters[category].append(i)

            from ..models import ArticleCluster
            clusters = []
            for category, indices in category_clusters.items():
                clusters.append(ArticleCluster(
                    cluster_id=category.lower().replace(" ", "_").replace("&", "and"),
                    theme=category,
                    description=f"Articles related to {category.lower()}",
                    article_indices=indices,
                    priority="Medium"
                ))

            return ArticleClustering(clusters=clusters)

    def generate_enhanced_daily_summary(self, articles: List[NewsArticle]) -> Dict[str, Any]:
        """Generate an enhanced daily summary with clustering and markdown formatting."""
        self.log_method_call("generate_enhanced_daily_summary", article_count=len(articles))

        try:
            if not articles:
                return {
                    "markdown_summary": "# Daily Carbon Regulation News Summary\n\nNo articles processed today.",
                    "summary": "No articles processed today.",
                    "key_developments": [],
                    "statistics": {"total_articles": 0},
                    "clusters": [],
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

            # Prepare article summaries for AI
            article_summaries = []
            for article in articles:
                if article.ai_summary and article.ai_classification:
                    article_summaries.append({
                        "title": article.title,
                        "summary": article.ai_summary,
                        "category": article.ai_classification.get("category", "Unknown"),
                        "type": article.ai_classification.get("type", "Unknown"),
                        "jurisdictions": article.ai_classification.get("jurisdictions", []),
                        "key_points": article.ai_key_points or [],
                        "source": article.source_name,
                        "url": article.url
                    })

            if not article_summaries:
                return {
                    "markdown_summary": "# Daily Carbon Regulation News Summary\n\nNo processed articles available for summary.",
                    "summary": "No processed articles available for summary.",
                    "key_developments": [],
                    "statistics": {"total_articles": len(articles), "processed_articles": 0},
                    "clusters": [],
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

            # Step 1: Cluster articles by themes
            clustering_result = self._cluster_articles(article_summaries)
            clusters = clustering_result.clusters

            # Step 2: Organize clustered articles for AI
            clustered_articles = []
            for cluster in clusters:
                cluster_articles = []
                for idx in cluster.article_indices:
                    if 0 <= idx < len(article_summaries):
                        cluster_articles.append(article_summaries[idx])

                if cluster_articles:
                    clustered_articles.append({
                        "cluster_id": cluster.cluster_id,
                        "theme": cluster.theme,
                        "description": cluster.description,
                        "priority": cluster.priority,
                        "articles": cluster_articles
                    })

            # Step 3: Compile statistics
            categories = {}
            types = {}
            jurisdictions = {}

            for article in articles:
                if article.ai_classification:
                    cat = article.ai_classification.get("category", "Unknown")
                    categories[cat] = categories.get(cat, 0) + 1

                    typ = article.ai_classification.get("type", "Unknown")
                    types[typ] = types.get(typ, 0) + 1

                    for jurisdiction in article.ai_classification.get("jurisdictions", []):
                        jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1

            # Step 4: Generate enhanced markdown summary
            try:
                prompt = self.settings.prompts.ai_parser.enhanced_summary_prompt_template.format(
                    clustered_articles=str(clustered_articles)[:8000],
                    total_articles=len(article_summaries),
                    categories=", ".join(categories.keys()),
                    jurisdictions=", ".join(jurisdictions.keys())
                )

                result = self._get_enhanced_summary_agent().run_sync(prompt)
                enhanced_summary = result.output

                markdown_summary = enhanced_summary.markdown_content

            except Exception as e:
                self.logger.warning("Enhanced summary generation failed, using fallback", error=str(e))
                markdown_summary = self._generate_fallback_markdown_summary(clustered_articles, categories, jurisdictions)

            # Step 5: Extract key developments from markdown for backward compatibility
            key_developments = self._extract_key_developments_from_markdown(markdown_summary)

            summary_result = {
                "markdown_summary": markdown_summary.strip(),
                "summary": self._extract_executive_summary_from_markdown(markdown_summary),
                "key_developments": key_developments,
                "clusters": clusters,
                "statistics": {
                    "total_articles": len(articles),
                    "processed_articles": len(article_summaries),
                    "cluster_count": len(clusters),
                    "categories": categories,
                    "types": types,
                    "jurisdictions": jurisdictions
                },
                "articles": article_summaries,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            self.logger.info("Enhanced daily summary generated",
                           total_articles=len(articles),
                           clusters=len(clusters),
                           markdown_length=len(markdown_summary))
            return summary_result

        except Exception as e:
            self.log_error(e)
            raise

    def _generate_fallback_markdown_summary(self, clustered_articles: List[Dict], categories: Dict, jurisdictions: Dict) -> str:
        """Generate a fallback markdown summary when AI generation fails."""
        today = datetime.now(timezone.utc).strftime('%B %d, %Y')

        markdown = f"# Daily Carbon Regulation News Summary\n*{today}*\n\n"

        # Executive Summary
        markdown += "## Executive Summary\n\n"
        total_articles = sum(len(cluster['articles']) for cluster in clustered_articles)
        markdown += f"Today's carbon regulation landscape features {total_articles} key developments "
        markdown += f"across {len(categories)} categories and {len(jurisdictions)} jurisdictions. "

        if clustered_articles:
            high_priority = [c for c in clustered_articles if c.get('priority') == 'High']
            if high_priority:
                markdown += f"High-priority developments include {len(high_priority)} critical updates "
                markdown += "requiring immediate attention from climate policy professionals.\n\n"
            else:
                markdown += "Key regulatory and market developments continue to shape the climate policy landscape.\n\n"

        # Key Developments by Theme
        markdown += "## Key Developments by Theme\n\n"

        for cluster in clustered_articles:
            theme = cluster.get('theme', 'Unknown Theme')
            description = cluster.get('description', '')
            articles = cluster.get('articles', [])

            markdown += f"### {theme}\n"
            if description:
                markdown += f"*{description}*\n\n"

            for article in articles:
                title = article.get('title', 'Untitled')
                source = article.get('source', 'Unknown Source')
                url = article.get('url', '#')
                summary = article.get('summary', 'No summary available')

                markdown += f"- **[{title}]({url})** ({source})\n"
                markdown += f"  {summary}\n\n"

        # Regional Focus
        if jurisdictions:
            markdown += "## Regional Focus\n\n"
            for jurisdiction, count in sorted(jurisdictions.items(), key=lambda x: x[1], reverse=True):
                markdown += f"- **{jurisdiction}**: {count} development{'s' if count != 1 else ''}\n"
            markdown += "\n"

        # Categories Overview
        if categories:
            markdown += "## Categories Overview\n\n"
            for category, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
                markdown += f"- **{category}**: {count} article{'s' if count != 1 else ''}\n"
            markdown += "\n"

        markdown += "---\n*Summary generated automatically from carbon regulation and climate policy news sources.*"

        return markdown

    def _extract_key_developments_from_markdown(self, markdown_summary: str) -> List[str]:
        """Extract key developments from markdown summary for backward compatibility."""
        import re

        key_developments = []

        # Look for bullet points and numbered lists
        bullet_pattern = r'^[-*+]\s+(.+)$'
        numbered_pattern = r'^\d+\.\s+(.+)$'

        lines = markdown_summary.split('\n')
        for line in lines:
            line = line.strip()

            # Match bullet points
            bullet_match = re.match(bullet_pattern, line)
            if bullet_match:
                # Clean up markdown formatting
                text = bullet_match.group(1)
                text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)  # Remove links, keep text
                text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)  # Remove bold
                text = re.sub(r'\*([^*]+)\*', r'\1', text)  # Remove italic
                if len(text) > 20:  # Only include substantial points
                    key_developments.append(text)

            # Match numbered lists
            numbered_match = re.match(numbered_pattern, line)
            if numbered_match:
                text = numbered_match.group(1)
                text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
                text = re.sub(r'\*\*([^*]+)\*\*', r'\1', text)
                text = re.sub(r'\*([^*]+)\*', r'\1', text)
                if len(text) > 20:
                    key_developments.append(text)

        return key_developments[:10]  # Limit to top 10 developments

    def _extract_executive_summary_from_markdown(self, markdown_summary: str) -> str:
        """Extract executive summary text from markdown for backward compatibility."""
        import re

        # Look for Executive Summary section
        lines = markdown_summary.split('\n')
        in_exec_summary = False
        summary_lines = []

        for line in lines:
            line = line.strip()

            if re.match(r'^#+\s*Executive Summary', line, re.IGNORECASE):
                in_exec_summary = True
                continue
            elif in_exec_summary and re.match(r'^#+\s', line):
                # Hit next section, stop
                break
            elif in_exec_summary and line:
                # Clean up markdown formatting
                clean_line = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', line)  # Remove links
                clean_line = re.sub(r'\*\*([^*]+)\*\*', r'\1', clean_line)  # Remove bold
                clean_line = re.sub(r'\*([^*]+)\*', r'\1', clean_line)  # Remove italic
                clean_line = re.sub(r'^[*-]\s*', '', clean_line)  # Remove bullet points
                if clean_line and not clean_line.startswith('#'):
                    summary_lines.append(clean_line)

        if summary_lines:
            return ' '.join(summary_lines)
        else:
            # Fallback: take first few sentences
            clean_text = re.sub(r'#+[^\n]*\n', '', markdown_summary)  # Remove headers
            clean_text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', clean_text)  # Remove links
            clean_text = re.sub(r'\*\*([^*]+)\*\*', r'\1', clean_text)  # Remove bold
            sentences = clean_text.split('.')[:3]  # First 3 sentences
            return '. '.join(s.strip() for s in sentences if s.strip()) + '.'

    def parse_article(self, article: NewsArticle) -> Dict[str, Any]:
        """Parse a single news article using AI."""
        self.log_method_call("parse_article", article_id=article.id)
        
        try:
            extract_input = ExtractInput(
                url=article.url,
                outlet=article.source_name,
                raw_content=article.content
            )
            
            # Extract classification
            classification_result = self._extract_classification(extract_input)

            # Extract content
            content_result = self._extract_content(extract_input)

            # Extract type-specific details if applicable
            details_result = None
            if classification_result.type != ItemType.UNKNOWN:
                details_result = self._extract_details(classification_result.type, extract_input.raw_content)

            # Prepare results
            parsing_result = {
                "classification": {
                    "category": classification_result.category.value,
                    "type": classification_result.type.value,
                    "jurisdictions": classification_result.jurisdictions,
                    "sectors": classification_result.sectors or []
                },
                "content": {
                    "title": content_result.title,
                    "summary": content_result.summary,
                    "key_points": content_result.key_points,
                    "original_text": content_result.original_text
                },
                "details": details_result.model_dump() if details_result else None,
                "parsed_at": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info(
                "Article parsed successfully",
                article_id=article.id,
                category=parsing_result["classification"]["category"],
                type=parsing_result["classification"]["type"]
            )

            return parsing_result

        except Exception as e:
            self.log_error(e, {"article_id": article.id, "article_url": article.url})
            raise

    def _extract_classification(self, data: ExtractInput) -> AIClassification:
        """Extract classification information using structured output."""
        prompt = self.settings.prompts.ai_parser.classification_prompt_template.format(
            url=data.url,
            source=data.outlet or 'Unknown',
            content=data.raw_content[:4000]
        )

        try:
            result = self._get_classification_agent().run_sync(prompt)
            return result.output
        except Exception as e:
            self.logger.warning("Classification extraction failed", error=str(e))
            # Return fallback classification
            return AIClassification(
                category=Category.UNKNOWN,
                type=ItemType.UNKNOWN,
                jurisdictions=[],
                sectors=[]
            )

    def _extract_content(self, data: ExtractInput) -> AIContent:
        """Extract normalized content using structured output."""
        prompt = self.settings.prompts.ai_parser.content_extraction_prompt_template.format(
            url=data.url,
            source=data.outlet or 'Unknown',
            content=data.raw_content[:6000]
        )

        try:
            result = self._get_content_agent().run_sync(prompt)
            return result.output
        except Exception as e:
            self.logger.warning("Content extraction failed", error=str(e))
            # Return fallback content
            return AIContent(
                title="Failed to extract title",
                summary="Failed to extract summary",
                key_points=[],
                original_text=data.raw_content[:8000]
            )

    def _extract_details(self, item_type: ItemType, text: str) -> Optional[Details]:
        """Extract type-specific details with error handling."""
        agent = self._get_detail_agent(item_type)
        if not agent:
            return None

        prompt = f"""Given the following article text, extract the specific fields requested by the schema.

Article text:
{text}
"""

        try:
            result = agent.run_sync(prompt)
            return result.output
        except Exception as e:
            self.logger.warning("Details extraction failed", error=str(e), item_type=item_type.value)
            # Graceful failure - return None if details extraction fails
            return None
    
    def update_article_with_ai_data(self, article: NewsArticle, parsing_result: Dict[str, Any]) -> NewsArticle:
        """Update article in database with AI parsing results."""
        self.log_method_call("update_article_with_ai_data", article_id=article.id)
        
        try:
            # Update article with AI results
            article.ai_classification = parsing_result["classification"]
            article.ai_summary = parsing_result["content"]["summary"]
            article.ai_key_points = parsing_result["content"]["key_points"]
            article.ai_details = parsing_result.get("details")
            article.is_processed = True
            article.processed_at = datetime.now(timezone.utc)
            
            self.db.commit()
            self.db.refresh(article)
            
            self.logger.info("Article updated with AI data", article_id=article.id)
            return article
            
        except Exception as e:
            self.db.rollback()
            self.log_error(e, {"article_id": article.id})
            raise
    
    def process_unprocessed_articles(self) -> Dict[str, Any]:
        """Process all unprocessed articles in the database."""
        self.log_method_call("process_unprocessed_articles")
        
        try:
            # Get unprocessed articles
            unprocessed_articles = self.db.query(NewsArticle).filter(
                NewsArticle.is_processed == False
            ).all()
            
            processed_count = 0
            failed_count = 0
            
            for article in unprocessed_articles:
                try:
                    # Parse article
                    parsing_result = self.parse_article(article)
                    
                    # Update article
                    self.update_article_with_ai_data(article, parsing_result)
                    processed_count += 1
                    
                except Exception as e:
                    failed_count += 1
                    self.log_error(e, {"article_id": article.id})
                    continue
            
            summary = {
                "total_unprocessed": len(unprocessed_articles),
                "processed_count": processed_count,
                "failed_count": failed_count,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            self.logger.info("Batch processing completed", **summary)
            return summary
            
        except Exception as e:
            self.log_error(e)
            raise
    
    def generate_daily_summary(self, articles: List[NewsArticle], use_enhanced: bool = True) -> Dict[str, Any]:
        """Generate a daily summary from processed articles.

        Args:
            articles: List of news articles to summarize
            use_enhanced: If True, use enhanced clustering and markdown generation (default)
                         If False, use legacy JSON-only generation for backward compatibility
        """
        self.log_method_call("generate_daily_summary", article_count=len(articles), use_enhanced=use_enhanced)

        if use_enhanced:
            # Use the new enhanced summary generation
            enhanced_result = self.generate_enhanced_daily_summary(articles)

            # Add legacy fields for backward compatibility
            enhanced_result.update({
                "executive_summary": enhanced_result.get("summary", ""),
                "regulatory_changes": [],  # Extract from markdown if needed
                "market_implications": [],  # Extract from markdown if needed
                "important_dates": []  # Extract from markdown if needed
            })

            return enhanced_result

        # Legacy implementation for backward compatibility
        try:
            if not articles:
                return {
                    "summary": "No articles processed today.",
                    "key_developments": [],
                    "statistics": {"total_articles": 0},
                    "generated_at": datetime.now(timezone.utc).isoformat()
                }

            # Prepare article summaries for AI
            article_summaries = []
            for article in articles:
                if article.ai_summary and article.ai_classification:
                    article_summaries.append({
                        "title": article.title,
                        "summary": article.ai_summary,
                        "category": article.ai_classification.get("category", "Unknown"),
                        "type": article.ai_classification.get("type", "Unknown"),
                        "jurisdictions": article.ai_classification.get("jurisdictions", []),
                        "key_points": article.ai_key_points or [],
                        "source": article.source_name,
                        "url": article.url
                    })

            # Generate summary using AI
            prompt = self.settings.prompts.ai_parser.daily_summary_prompt_template.format(
                article_count=len(article_summaries),
                article_summaries=str(article_summaries)[:8000]
            )

            try:
                result = self._get_summary_agent().run_sync(prompt)
                ai_summary = result.output
            except Exception as e:
                self.logger.warning("AI summary generation failed", error=str(e))
                ai_summary = DailySummary(
                    executive_summary="Failed to generate AI summary",
                    key_developments=[],
                    regulatory_changes=[],
                    market_implications=[],
                    important_dates=[]
                )

            # Compile statistics
            categories = {}
            types = {}
            jurisdictions = {}

            for article in articles:
                if article.ai_classification:
                    cat = article.ai_classification.get("category", "Unknown")
                    categories[cat] = categories.get(cat, 0) + 1

                    typ = article.ai_classification.get("type", "Unknown")
                    types[typ] = types.get(typ, 0) + 1

                    for jurisdiction in article.ai_classification.get("jurisdictions", []):
                        jurisdictions[jurisdiction] = jurisdictions.get(jurisdiction, 0) + 1

            summary_result = {
                "executive_summary": ai_summary.executive_summary,
                "key_developments": ai_summary.key_developments,
                "regulatory_changes": ai_summary.regulatory_changes,
                "market_implications": ai_summary.market_implications,
                "important_dates": ai_summary.important_dates,
                "statistics": {
                    "total_articles": len(articles),
                    "processed_articles": len(article_summaries),
                    "categories": categories,
                    "types": types,
                    "jurisdictions": jurisdictions
                },
                "articles": article_summaries,
                "generated_at": datetime.now(timezone.utc).isoformat()
            }

            self.logger.info("Daily summary generated (legacy mode)", total_articles=len(articles))
            return summary_result

        except Exception as e:
            self.log_error(e)
            raise
    
    def __del__(self):
        """Clean up database session."""
        if hasattr(self, 'db') and self.db:
            self.db.close()
