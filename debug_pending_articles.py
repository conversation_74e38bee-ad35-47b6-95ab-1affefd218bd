#!/usr/bin/env python3
"""
Debug script to identify why articles are stuck in pending state.
"""

import sys
import os
from datetime import datetime, timezone, timedelta
from sqlalchemy import text

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database import get_db_session
from app.core.models import NewsArticle, TaskExecution
from app.services.ai_parser import AINewsParsingService


def analyze_pending_articles():
    """Analyze articles stuck in pending state."""
    print("🔍 Analyzing articles stuck in pending state...")
    
    db = get_db_session()
    try:
        # Get all unprocessed articles
        unprocessed = db.query(NewsArticle).filter(
            NewsArticle.is_processed == False
        ).all()
        
        print(f"\n📊 Found {len(unprocessed)} unprocessed articles")
        
        if not unprocessed:
            print("✅ No pending articles found!")
            return
        
        # Analyze by age
        now = datetime.now(timezone.utc)
        age_buckets = {
            "< 1 hour": 0,
            "1-6 hours": 0,
            "6-24 hours": 0,
            "> 24 hours": 0
        }
        
        for article in unprocessed:
            # Handle timezone-aware vs naive datetime comparison
            collected_at = article.collected_at
            if collected_at.tzinfo is None:
                collected_at = collected_at.replace(tzinfo=timezone.utc)

            age = now - collected_at
            if age < timedelta(hours=1):
                age_buckets["< 1 hour"] += 1
            elif age < timedelta(hours=6):
                age_buckets["1-6 hours"] += 1
            elif age < timedelta(hours=24):
                age_buckets["6-24 hours"] += 1
            else:
                age_buckets["> 24 hours"] += 1
        
        print("\n⏰ Articles by age:")
        for bucket, count in age_buckets.items():
            print(f"  {bucket}: {count}")
        
        # Show oldest pending articles
        oldest = sorted(unprocessed, key=lambda x: x.collected_at)[:5]
        print(f"\n🕰️  Oldest {len(oldest)} pending articles:")
        for article in oldest:
            collected_at = article.collected_at
            if collected_at.tzinfo is None:
                collected_at = collected_at.replace(tzinfo=timezone.utc)
            age = now - collected_at
            print(f"  ID {article.id}: {article.title[:60]}... (age: {age})")
        
        # Check for recent processing attempts
        recent_tasks = db.query(TaskExecution).filter(
            TaskExecution.task_type.in_(["ai_processing", "full_pipeline"]),
            TaskExecution.started_at > now - timedelta(hours=24)
        ).order_by(TaskExecution.started_at.desc()).limit(10).all()
        
        print(f"\n🔄 Recent processing tasks (last 24h): {len(recent_tasks)}")
        for task in recent_tasks:
            print(f"  {task.task_type} - {task.status} - {task.started_at}")
            if task.error_message:
                print(f"    Error: {task.error_message}")
        
        return unprocessed
        
    finally:
        db.close()


def test_single_article_processing():
    """Test processing a single article to identify issues."""
    print("\n🧪 Testing single article processing...")
    
    db = get_db_session()
    try:
        # Get the oldest unprocessed article
        article = db.query(NewsArticle).filter(
            NewsArticle.is_processed == False
        ).order_by(NewsArticle.collected_at).first()
        
        if not article:
            print("❌ No unprocessed articles to test")
            return
        
        print(f"📄 Testing article ID {article.id}: {article.title[:60]}...")
        
        # Test AI parsing service
        ai_service = AINewsParsingService(db)
        
        try:
            # Test parsing
            print("  🤖 Testing AI parsing...")
            parsing_result = ai_service.parse_article(article)
            print("  ✅ Parsing successful")
            
            # Test database update
            print("  💾 Testing database update...")
            updated_article = ai_service.update_article_with_ai_data(article, parsing_result)
            print("  ✅ Database update successful")
            
            print(f"  📊 Result: {updated_article.ai_classification}")
            
        except Exception as e:
            print(f"  ❌ Error during processing: {e}")
            import traceback
            traceback.print_exc()
            
    finally:
        db.close()


def check_database_locks():
    """Check for database locks or connection issues."""
    print("\n🔒 Checking database locks and connections...")
    
    db = get_db_session()
    try:
        # Test basic query
        result = db.execute(text("SELECT COUNT(*) FROM news_articles")).scalar()
        print(f"  📊 Total articles in database: {result}")
        
        # Test write operation
        db.execute(text("UPDATE news_articles SET collected_at = collected_at WHERE 1=0"))
        db.commit()
        print("  ✅ Database write test successful")
        
        # Check for long-running transactions (PostgreSQL specific)
        try:
            long_transactions = db.execute(text("""
                SELECT pid, state, query_start, query 
                FROM pg_stat_activity 
                WHERE state != 'idle' AND query_start < NOW() - INTERVAL '5 minutes'
            """)).fetchall()
            
            if long_transactions:
                print(f"  ⚠️  Found {len(long_transactions)} long-running transactions")
                for tx in long_transactions:
                    print(f"    PID {tx[0]}: {tx[1]} - {tx[3][:60]}...")
            else:
                print("  ✅ No long-running transactions found")
                
        except Exception:
            # Not PostgreSQL or query not supported
            print("  ℹ️  Long-running transaction check not available (likely SQLite)")
        
    except Exception as e:
        print(f"  ❌ Database check failed: {e}")
        
    finally:
        db.close()


def check_ai_service_health():
    """Check AI service configuration and health."""
    print("\n🤖 Checking AI service health...")
    
    try:
        from app.services.ai_parser import ModelManager
        from app.core.config import get_settings
        
        settings = get_settings()
        
        # Check API keys
        if hasattr(settings, 'openrouter_api_key') and settings.openrouter_api_key:
            print("  ✅ OpenRouter API key configured")
        else:
            print("  ❌ OpenRouter API key missing")
        
        # Test model creation
        try:
            model = ModelManager.get_model("classifier")
            print("  ✅ AI model creation successful")
        except Exception as e:
            print(f"  ❌ AI model creation failed: {e}")
        
        # Test agent creation
        try:
            db = get_db_session()
            ai_service = AINewsParsingService(db)
            agent = ai_service._get_classification_agent()
            print("  ✅ AI agent creation successful")
            db.close()
        except Exception as e:
            print(f"  ❌ AI agent creation failed: {e}")
            
    except Exception as e:
        print(f"  ❌ AI service check failed: {e}")


def main():
    """Main diagnostic function."""
    print("🚀 Carbon Regulation News - Pending Articles Diagnostic")
    print("=" * 60)
    
    try:
        # Run all diagnostic checks
        pending_articles = analyze_pending_articles()
        check_database_locks()
        check_ai_service_health()
        
        if pending_articles:
            test_single_article_processing()
        
        print("\n" + "=" * 60)
        print("🏁 Diagnostic complete!")
        
        if pending_articles and len(pending_articles) > 0:
            print(f"\n💡 Recommendations:")
            print(f"   1. Check AI service configuration and API keys")
            print(f"   2. Review recent error logs for processing failures")
            print(f"   3. Consider manually triggering AI processing task")
            print(f"   4. Check database connection and transaction handling")
        
    except Exception as e:
        print(f"\n❌ Diagnostic failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
