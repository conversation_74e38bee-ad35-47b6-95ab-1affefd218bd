#!/usr/bin/env python3
"""
Management script for handling stuck articles and processing issues.
"""

import sys
import os
import argparse
from datetime import datetime, timezone

# Add the app directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core.database import get_db_session
from app.core.models import NewsArticle
from app.services.ai_parser import AINewsParsingService


def check_stuck_articles(hours: int = 6):
    """Check for articles stuck in pending state."""
    print(f"🔍 Checking for articles stuck longer than {hours} hours...")
    
    db = get_db_session()
    try:
        ai_service = AINewsParsingService(db)
        result = ai_service.check_stuck_articles(hours_threshold=hours)
        
        print(f"📊 Found {result['stuck_count']} stuck articles")
        
        if result['stuck_articles']:
            print("\n🕰️  Stuck articles:")
            for article in result['stuck_articles']:
                print(f"  ID {article['id']}: {article['title']} (age: {article['age_hours']:.1f}h)")
        
        return result
        
    finally:
        db.close()


def retry_failed_articles(max_retries: int = 10):
    """Retry processing failed articles."""
    print(f"🔄 Retrying up to {max_retries} failed articles...")
    
    db = get_db_session()
    try:
        ai_service = AINewsParsingService(db)
        result = ai_service.retry_failed_articles(max_retries=max_retries)
        
        print(f"✅ Processed {result['processed_count']} articles")
        print(f"❌ Failed {result['failed_count']} articles")
        
        if result['failed_articles']:
            print("\n💥 Failed articles:")
            for failed in result['failed_articles']:
                print(f"  ID {failed['article_id']}: {failed['title']}")
                print(f"    Error: {failed['error']}")
        
        return result
        
    finally:
        db.close()


def process_all_pending(max_articles: int = None):
    """Process all pending articles."""
    print(f"⚡ Processing {'all' if not max_articles else f'up to {max_articles}'} pending articles...")
    
    db = get_db_session()
    try:
        ai_service = AINewsParsingService(db)
        result = ai_service.process_unprocessed_articles(max_articles=max_articles)
        
        print(f"📊 Total unprocessed: {result['total_unprocessed']}")
        print(f"✅ Successfully processed: {result['processed_count']}")
        print(f"❌ Failed: {result['failed_count']}")
        
        if result['failed_articles']:
            print("\n💥 Failed articles:")
            for failed in result['failed_articles']:
                print(f"  ID {failed['article_id']}: {failed['title']}")
                print(f"    Error: {failed['error']}")
        
        return result
        
    finally:
        db.close()


def show_article_stats():
    """Show article processing statistics."""
    print("📊 Article Processing Statistics")
    print("=" * 40)
    
    db = get_db_session()
    try:
        total = db.query(NewsArticle).count()
        processed = db.query(NewsArticle).filter(NewsArticle.is_processed == True).count()
        pending = total - processed
        
        print(f"Total articles: {total}")
        print(f"Processed: {processed}")
        print(f"Pending: {pending}")
        
        if total > 0:
            print(f"Processing rate: {(processed/total)*100:.1f}%")
        
        # Show recent articles
        recent = db.query(NewsArticle).order_by(NewsArticle.collected_at.desc()).limit(5).all()
        print(f"\n📰 Recent articles:")
        for article in recent:
            status = "✅" if article.is_processed else "⏳"
            print(f"  {status} ID {article.id}: {article.title[:60]}...")
        
    finally:
        db.close()


def reset_article_status(article_id: int):
    """Reset an article's processing status."""
    print(f"🔄 Resetting article {article_id} status...")
    
    db = get_db_session()
    try:
        article = db.query(NewsArticle).filter(NewsArticle.id == article_id).first()
        if not article:
            print(f"❌ Article {article_id} not found")
            return
        
        article.is_processed = False
        article.processed_at = None
        article.ai_classification = None
        article.ai_summary = None
        article.ai_key_points = None
        article.ai_details = None
        
        db.commit()
        print(f"✅ Article {article_id} status reset")
        
    except Exception as e:
        db.rollback()
        print(f"❌ Failed to reset article {article_id}: {e}")
        
    finally:
        db.close()


def main():
    """Main function with command line interface."""
    parser = argparse.ArgumentParser(description="Manage article processing")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Check stuck articles
    check_parser = subparsers.add_parser('check', help='Check for stuck articles')
    check_parser.add_argument('--hours', type=int, default=6, help='Hours threshold for stuck articles')
    
    # Retry failed articles
    retry_parser = subparsers.add_parser('retry', help='Retry failed articles')
    retry_parser.add_argument('--max', type=int, default=10, help='Maximum articles to retry')
    
    # Process all pending
    process_parser = subparsers.add_parser('process', help='Process pending articles')
    process_parser.add_argument('--max', type=int, help='Maximum articles to process')
    
    # Show statistics
    subparsers.add_parser('stats', help='Show article statistics')
    
    # Reset article
    reset_parser = subparsers.add_parser('reset', help='Reset article processing status')
    reset_parser.add_argument('article_id', type=int, help='Article ID to reset')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print("🚀 Carbon Regulation News - Article Management")
    print("=" * 50)
    
    try:
        if args.command == 'check':
            check_stuck_articles(args.hours)
        elif args.command == 'retry':
            retry_failed_articles(args.max)
        elif args.command == 'process':
            process_all_pending(args.max)
        elif args.command == 'stats':
            show_article_stats()
        elif args.command == 'reset':
            reset_article_status(args.article_id)
        
        print("\n✅ Command completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Command failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
