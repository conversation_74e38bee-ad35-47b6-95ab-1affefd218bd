"""
Integration tests for the complete news collection and processing pipeline.
"""

import pytest
from unittest.mock import patch, Mock
from datetime import datetime, timezone

from app.services.scheduler import TaskSchedulerService, TaskType
from app.services.news_collector import NewsCollectionService
from app.services.ai_parser import AINewsParsingService
from app.services.notifications import NotificationService
from app.core.models import NewsArticle, TaskExecution, TaskResult
from app.models import (
    AIClassification, AIContent, DailySummary, Category, ItemType,
    ArticleClustering, ArticleCluster, EnhancedSummary
)


class TestFullPipeline:
    """Integration tests for the complete pipeline."""
    
    @patch('app.services.news_collector.TavilyClient')
    @patch('app.services.ai_parser.Agent')
    @patch('app.services.notifications.requests')
    def test_complete_pipeline_success(self, mock_requests, mock_agent, mock_tavily, db_session):
        """Test the complete pipeline from collection to notification."""
        
        # Setup mocks for news collection
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.search.return_value = {
            "results": [
                {
                    "url": "https://example.com/carbon-tax-news",
                    "title": "New Carbon Tax Legislation",
                    "content": "The government announced new carbon tax regulations..."
                },
                {
                    "url": "https://example.com/ets-update",
                    "title": "EU ETS Update",
                    "content": "The European Union updated its emissions trading system..."
                }
            ]
        }
        mock_tavily_instance.extract.return_value = {
            "results": [{"content": "Full article content with detailed information."}]
        }
        
        # Setup mocks for AI processing
        mock_agent_instance = Mock()
        mock_agent.return_value = mock_agent_instance
        
        def mock_ai_response(prompt):
            mock_result = Mock()
            if "classification" in prompt.lower():
                mock_result.output = AIClassification(
                    category=Category.CARBON_PRICING_MARKETS,
                    type=ItemType.LEGISLATION,
                    jurisdictions=["US"],
                    sectors=["power", "industry"]
                )
            elif "content" in prompt.lower():
                mock_result.output = AIContent(
                    title="Processed Article Title",
                    summary="AI-generated summary of the article",
                    key_points=["Key point 1", "Key point 2", "Key point 3"],
                    original_text="Cleaned article text"
                )
            elif "cluster" in prompt.lower():
                mock_result.output = ArticleClustering(
                    clusters=[
                        ArticleCluster(
                            cluster_id="carbon_pricing",
                            theme="Carbon Pricing",
                            description="Articles about carbon pricing mechanisms",
                            article_indices=[0, 1],
                            priority="High"
                        )
                    ]
                )
            elif "enhanced" in prompt.lower():
                mock_result.output = EnhancedSummary(
                    markdown_content="# Daily Summary\n\nToday's developments...",
                    executive_summary="Daily summary of carbon regulation developments",
                    key_themes=["Carbon pricing", "Regulation"],
                    priority_items=["New legislation"]
                )
            else:  # summary
                mock_result.output = DailySummary(
                    executive_summary="Daily summary of carbon regulation developments",
                    key_developments=["Development 1", "Development 2"],
                    regulatory_changes=["Change 1"],
                    market_implications=["Implication 1"],
                    important_dates=["Date 1"]
                )
            return mock_result
        
        mock_agent_instance.run_sync.side_effect = mock_ai_response
        
        # Setup mocks for notifications
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "OK"
        mock_requests.post.return_value = mock_response
        
        # Execute the full pipeline
        scheduler_service = TaskSchedulerService(db_session)
        task_execution = scheduler_service.execute_task(TaskType.FULL_PIPELINE, "test_full_pipeline")
        
        # Verify task execution
        assert task_execution.status == "success"
        assert task_execution.duration_seconds is not None
        assert task_execution.result_summary is not None
        
        # Verify pipeline results
        result_summary = task_execution.result_summary
        assert "collection" in result_summary
        assert "processing" in result_summary
        assert "summary" in result_summary
        
        # Verify articles were collected and saved
        articles = db_session.query(NewsArticle).all()
        assert len(articles) >= 2
        
        # Verify articles were processed
        processed_articles = db_session.query(NewsArticle).filter(
            NewsArticle.is_processed == True
        ).all()
        assert len(processed_articles) >= 2
        
        # Verify AI data was added
        for article in processed_articles:
            assert article.ai_classification is not None
            assert article.ai_summary is not None
            assert article.ai_key_points is not None
        
        # Verify task result was created
        task_results = db_session.query(TaskResult).filter(
            TaskResult.task_execution_id == task_execution.id
        ).all()
        assert len(task_results) >= 1
        
        # Find the daily summary result
        summary_result = next(
            (r for r in task_results if r.result_type == "daily_summary"), 
            None
        )
        assert summary_result is not None
        assert summary_result.title.startswith("Daily Carbon Regulation News Summary")
        assert summary_result.result_metadata is not None
    
    @patch('app.services.news_collector.TavilyClient')
    def test_news_collection_only(self, mock_tavily, db_session):
        """Test news collection task in isolation."""
        
        # Setup mocks
        mock_tavily_instance = Mock()
        mock_tavily.return_value = mock_tavily_instance
        mock_tavily_instance.search.return_value = {
            "results": [
                {
                    "url": "https://example.com/test-article",
                    "title": "Test Article",
                    "content": "Test content"
                }
            ]
        }
        mock_tavily_instance.extract.return_value = {
            "results": [{"content": "Full test content"}]
        }
        
        # Execute news collection task
        scheduler_service = TaskSchedulerService(db_session)
        task_execution = scheduler_service.execute_task(TaskType.NEWS_COLLECTION, "test_collection")
        
        # Verify task execution
        assert task_execution.status == "success"
        assert task_execution.result_summary is not None
        
        # Verify articles were collected
        result = task_execution.result_summary
        assert result["saved_count"] >= 1
        assert len(result["sources"]) >= 1
        
        # Verify in database
        articles = db_session.query(NewsArticle).all()
        assert len(articles) >= 1
        assert not any(article.is_processed for article in articles)
    
    @patch('app.services.ai_parser.Agent')
    def test_ai_processing_only(self, mock_agent, db_session):
        """Test AI processing task in isolation."""
        
        # Create unprocessed articles
        article1 = NewsArticle(
            title="Test Article 1",
            url="https://example.com/article1",
            content="Test content 1",
            source_name="Test Source",
            collected_at=datetime.now(timezone.utc),
            is_processed=False
        )
        article2 = NewsArticle(
            title="Test Article 2",
            url="https://example.com/article2",
            content="Test content 2",
            source_name="Test Source",
            collected_at=datetime.now(timezone.utc),
            is_processed=False
        )
        
        db_session.add_all([article1, article2])
        db_session.commit()
        
        # Setup AI mocks
        mock_agent_instance = Mock()
        mock_agent.return_value = mock_agent_instance
        
        def mock_ai_response(prompt):
            mock_result = Mock()
            if "classification" in prompt.lower():
                mock_result.output = AIClassification(
                    category=Category.CARBON_PRICING_MARKETS,
                    type=ItemType.REGULATORY_UPDATE,
                    jurisdictions=["US"],
                    sectors=["power"]
                )
            else:  # content
                mock_result.output = AIContent(
                    title="Processed Title",
                    summary="AI summary",
                    key_points=["Point 1", "Point 2"],
                    original_text="Cleaned text"
                )
            return mock_result
        
        mock_agent_instance.run_sync.side_effect = mock_ai_response
        
        # Execute AI processing task
        scheduler_service = TaskSchedulerService(db_session)
        task_execution = scheduler_service.execute_task(TaskType.AI_PROCESSING, "test_processing")
        
        # Verify task execution
        assert task_execution.status == "success"
        assert task_execution.result_summary is not None
        
        # Verify processing results
        result = task_execution.result_summary
        assert result["total_unprocessed"] == 2
        assert result["processed_count"] == 2
        assert result["failed_count"] == 0
        
        # Verify articles are processed
        db_session.refresh(article1)
        db_session.refresh(article2)
        assert article1.is_processed == True
        assert article2.is_processed == True
        assert article1.ai_classification is not None
        assert article2.ai_classification is not None
    
    @patch('app.services.ai_parser.Agent')
    def test_daily_summary_generation(self, mock_agent, db_session, processed_news_article):
        """Test daily summary generation task."""
        
        # Setup AI mock for summary generation
        mock_agent_instance = Mock()
        mock_agent.return_value = mock_agent_instance
        
        mock_result = Mock()
        mock_result.output = DailySummary(
            executive_summary="Today's carbon regulation developments include new legislation and market updates.",
            key_developments=["New carbon tax proposal", "ETS price increase"],
            regulatory_changes=["Updated emission standards"],
            market_implications=["Increased compliance costs"],
            important_dates=["Implementation deadline: 2025-01-01"]
        )
        mock_agent_instance.run_sync.return_value = mock_result
        
        # Execute daily summary task
        scheduler_service = TaskSchedulerService(db_session)
        task_execution = scheduler_service.execute_task(TaskType.DAILY_SUMMARY, "test_summary")
        
        # Verify task execution
        assert task_execution.status == "success"
        assert task_execution.result_summary is not None
        
        # Verify summary was created
        task_results = db_session.query(TaskResult).filter(
            TaskResult.task_execution_id == task_execution.id,
            TaskResult.result_type == "daily_summary"
        ).all()
        assert len(task_results) == 1
        
        summary_result = task_results[0]
        assert "Daily Summary" in summary_result.title
        assert summary_result.result_metadata is not None
        assert "executive_summary" in summary_result.result_metadata
    
    def test_pipeline_error_handling(self, db_session):
        """Test pipeline error handling and recovery."""

        # Create scheduler service first with valid settings
        scheduler_service = TaskSchedulerService(db_session)

        # Test with a task that will fail during execution by mocking a service method
        with patch.object(scheduler_service.news_collector, 'collect_and_save_news', side_effect=Exception("Simulated error")):
            # The execute_task method re-raises exceptions, so we need to catch it
            with pytest.raises(Exception, match="Simulated error"):
                scheduler_service.execute_task(TaskType.NEWS_COLLECTION, "test_error")

            # Check that the task execution was properly recorded as failed
            task_execution = db_session.query(TaskExecution).filter_by(task_name="test_error").first()
            assert task_execution is not None
            assert task_execution.status == "failed"
            assert task_execution.error_message == "Simulated error"
            assert task_execution.duration_seconds is not None
    
    @patch('app.services.notifications.requests')
    def test_notification_integration(self, mock_requests, db_session, sample_task_execution):
        """Test notification system integration."""
        
        # Setup notification mock
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.text = "OK"
        mock_requests.post.return_value = mock_response
        
        # Test notification service
        notification_service = NotificationService(db_session)
        
        # Add a webhook channel
        from app.services.notifications import WebhookNotificationChannel
        webhook_channel = WebhookNotificationChannel({"url": "https://example.com/webhook"})
        notification_service.add_channel("test_webhook", webhook_channel)
        
        # Send notification
        result = notification_service.send_task_success_notification(
            sample_task_execution,
            {"test": "data"}
        )
        
        # Verify notification was sent
        mock_requests.post.assert_called_once()
        
        # Verify notification log was created
        from app.core.models import NotificationLog
        logs = db_session.query(NotificationLog).all()
        assert len(logs) >= 1
    
    def test_database_transaction_handling(self, db_session):
        """Test database transaction handling in pipeline."""
        
        # Create a scenario that might cause database issues
        with patch('app.services.news_collector.NewsCollectionService.save_articles_to_database') as mock_save:
            mock_save.side_effect = Exception("Database error")
            
            scheduler_service = TaskSchedulerService(db_session)
            
            # Should handle database errors gracefully
            with pytest.raises(Exception):
                scheduler_service.execute_task(TaskType.NEWS_COLLECTION, "test_db_error")
            
            # Verify task execution was still recorded
            task_executions = db_session.query(TaskExecution).filter(
                TaskExecution.task_name == "test_db_error"
            ).all()
            assert len(task_executions) >= 1
            assert task_executions[0].status == "failed"
    
    def test_concurrent_task_execution(self, db_session):
        """Test handling of concurrent task executions."""
        
        # This would test thread safety and concurrent access
        # For now, we'll test that multiple tasks can be created
        scheduler_service = TaskSchedulerService(db_session)
        
        # Create multiple task executions
        task1 = TaskExecution(
            task_name="concurrent_test_1",
            task_type="news_collection",
            status="running",
            started_at=datetime.now(timezone.utc)
        )
        task2 = TaskExecution(
            task_name="concurrent_test_2",
            task_type="ai_processing",
            status="running",
            started_at=datetime.now(timezone.utc)
        )
        
        db_session.add_all([task1, task2])
        db_session.commit()
        
        # Verify both tasks exist
        tasks = db_session.query(TaskExecution).filter(
            TaskExecution.task_name.like("concurrent_test_%")
        ).all()
        assert len(tasks) == 2
    
    def test_data_consistency(self, db_session):
        """Test data consistency across the pipeline."""
        
        # Create test data
        article = NewsArticle(
            title="Consistency Test Article",
            url="https://example.com/consistency-test",
            content="Test content for consistency",
            source_name="Test Source",
            collected_at=datetime.now(timezone.utc),
            is_processed=False
        )
        
        db_session.add(article)
        db_session.commit()
        
        # Verify article exists and is unprocessed
        assert article.id is not None
        assert article.is_processed == False
        assert article.ai_classification is None
        
        # Simulate processing
        article.is_processed = True
        article.processed_at = datetime.now(timezone.utc)
        article.ai_classification = {"category": "Test Category"}
        article.ai_summary = "Test summary"
        
        db_session.commit()
        db_session.refresh(article)
        
        # Verify consistency
        assert article.is_processed == True
        assert article.processed_at is not None
        assert article.ai_classification is not None
        assert article.ai_summary == "Test summary"
