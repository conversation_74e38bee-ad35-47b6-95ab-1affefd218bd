"""
Unit tests for the AI news parsing service.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from app.services.ai_parser import AINewsParsingService, ModelManager
from app.core.models import NewsArticle
from app.models import AIClassification, AIContent, Category, ItemType
from tests.conftest import TEST_AI_CLASSIFICATION, TEST_AI_SUMMARY, TEST_AI_KEY_POINTS


class TestModelManager:
    """Test cases for ModelManager singleton."""
    
    def test_singleton_behavior(self):
        """Test that ModelManager returns the same instance."""
        with patch('app.services.ai_parser.OpenAIChatModel') as mock_model:
            mock_instance = Mock()
            mock_model.return_value = mock_instance

            # Reset models cache
            ModelManager._models = {}

            # Get instances
            instance1 = ModelManager.get_model()
            instance2 = ModelManager.get_model()

            # Should be the same instance
            assert instance1 is instance2
            assert instance1 is mock_instance

            # Model should only be created once
            mock_model.assert_called_once()
    
    def test_missing_api_key(self):
        """Test error when API key is missing."""
        with patch('app.services.ai_parser.get_settings') as mock_settings:
            mock_settings.return_value.openrouter_api_key = None

            # Reset models cache
            ModelManager._models = {}

            with pytest.raises(RuntimeError, match="Missing OPENROUTER_API_KEY"):
                ModelManager.get_model()


class TestAINewsParsingService:
    """Test cases for AINewsParsingService."""
    
    def test_init(self, db_session):
        """Test service initialization."""
        service = AINewsParsingService(db_session)

        assert service.db == db_session
        assert service.settings is not None
        assert service._agents == {}
    
    def test_get_classification_agent(self, db_session):
        """Test classification agent creation and caching."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            service = AINewsParsingService(db_session)
            
            # First call should create agent
            agent1 = service._get_classification_agent()
            assert agent1 == mock_agent_instance
            mock_agent.assert_called_once()
            
            # Second call should return cached agent
            agent2 = service._get_classification_agent()
            assert agent2 == mock_agent_instance
            assert agent1 is agent2
            # Agent constructor should still only be called once
            assert mock_agent.call_count == 1
    
    def test_get_content_agent(self, db_session):
        """Test content agent creation and caching."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            service = AINewsParsingService(db_session)
            
            # Test agent creation and caching
            agent1 = service._get_content_agent()
            agent2 = service._get_content_agent()
            
            assert agent1 == mock_agent_instance
            assert agent1 is agent2
            mock_agent.assert_called_once()
    
    def test_extract_classification_success(self, db_session, sample_news_article):
        """Test successful classification extraction."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:

            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance

            mock_result = Mock()
            mock_result.output = AIClassification(
                category=Category.CARBON_PRICING_MARKETS,
                type=ItemType.LEGISLATION,
                jurisdictions=["US"],
                sectors=["power", "industry", "transport"]
            )
            mock_agent_instance.run_sync.return_value = mock_result

            service = AINewsParsingService(db_session)
            service._agents['classification'] = mock_agent_instance

            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )

            result = service._extract_classification(extract_input)

            # Assertions
            assert result.category == Category.CARBON_PRICING_MARKETS
            assert result.type == ItemType.LEGISLATION
            assert result.jurisdictions == ["US"]
            assert result.sectors == ["power", "industry", "transport"]
            mock_agent_instance.run_sync.assert_called_once()
    
    def test_extract_classification_failure(self, db_session, sample_news_article):
        """Test classification extraction with AI failure."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            mock_agent_instance.run_sync.side_effect = Exception("AI Error")
            
            service = AINewsParsingService(db_session)
            service._agents['classification'] = mock_agent_instance
            
            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )
            
            result = service._extract_classification(extract_input)

            # Should return default values on failure
            assert result.category == Category.UNKNOWN
            assert result.type == ItemType.UNKNOWN
            assert result.jurisdictions == []
            assert result.sectors == []
    
    def test_extract_content_success(self, db_session, sample_news_article):
        """Test successful content extraction."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            # Setup mocks
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            mock_result = Mock()
            mock_result.output = AIContent(
                title="Extracted Title",
                summary=TEST_AI_SUMMARY,
                key_points=TEST_AI_KEY_POINTS,
                original_text="Cleaned original text"
            )
            mock_agent_instance.run_sync.return_value = mock_result
            
            service = AINewsParsingService(db_session)
            service._agents['content'] = mock_agent_instance
            
            # Test
            from app.services.ai_parser import ExtractInput
            extract_input = ExtractInput(
                url=sample_news_article.url,
                outlet=sample_news_article.source_name,
                raw_content=sample_news_article.content
            )
            
            result = service._extract_content(extract_input)
            
            # Assertions
            assert result.title == "Extracted Title"
            assert result.summary == TEST_AI_SUMMARY
            assert result.key_points == TEST_AI_KEY_POINTS
            assert result.original_text == "Cleaned original text"
            mock_agent_instance.run_sync.assert_called_once()
    
    def test_parse_article_success(self, db_session, sample_news_article):
        """Test successful article parsing."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)

            # Mock the extraction methods with structured output
            mock_classification = AIClassification(
                category=Category.CARBON_PRICING_MARKETS,
                type=ItemType.REGULATORY_UPDATE,
                jurisdictions=["US"],
                sectors=["power"]
            )
            mock_content = AIContent(
                title="Parsed Title",
                summary=TEST_AI_SUMMARY,
                key_points=TEST_AI_KEY_POINTS,
                original_text="Cleaned text"
            )

            service._extract_classification = Mock(return_value=mock_classification)
            service._extract_content = Mock(return_value=mock_content)
            service._extract_details = Mock(return_value=None)

            # Test
            result = service.parse_article(sample_news_article)

            # Assertions
            assert "classification" in result
            assert "content" in result
            assert "parsed_at" in result
            assert result["classification"]["category"] == "Carbon pricing & markets"
            assert result["classification"]["type"] == "Regulatory Update (Final)"
            assert result["content"]["summary"] == TEST_AI_SUMMARY

            service._extract_classification.assert_called_once()
            service._extract_content.assert_called_once()
    
    def test_update_article_with_ai_data(self, db_session, sample_news_article):
        """Test updating article with AI parsing results."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Prepare parsing result
            parsing_result = {
                "classification": {
                    "category": "Carbon pricing & markets",
                    "type": "Regulatory Update (Final)",
                    "jurisdictions": ["US"],
                    "sectors": ["power"]
                },
                "content": {
                    "title": "Parsed Title",
                    "summary": TEST_AI_SUMMARY,
                    "key_points": TEST_AI_KEY_POINTS,
                    "original_text": "Cleaned text"
                },
                "details": None
            }
            
            # Test
            updated_article = service.update_article_with_ai_data(sample_news_article, parsing_result)
            
            # Assertions
            assert updated_article.is_processed == True
            assert updated_article.processed_at is not None
            assert updated_article.ai_classification == parsing_result["classification"]
            assert updated_article.ai_summary == TEST_AI_SUMMARY
            assert updated_article.ai_key_points == TEST_AI_KEY_POINTS
            
            # Verify in database
            db_session.refresh(sample_news_article)
            assert sample_news_article.is_processed == True
    
    def test_process_unprocessed_articles(self, db_session):
        """Test processing all unprocessed articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            # Create unprocessed articles
            article1 = NewsArticle(
                title="Article 1",
                url="https://example.com/article1",
                content="Content 1",
                source_name="Source 1",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            article2 = NewsArticle(
                title="Article 2",
                url="https://example.com/article2",
                content="Content 2",
                source_name="Source 2",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            
            db_session.add_all([article1, article2])
            db_session.commit()
            
            service = AINewsParsingService(db_session)
            
            # Mock the parse_article method
            service.parse_article = Mock(return_value={
                "classification": {
                    "category": "Carbon pricing & markets",
                    "type": "Regulatory Update (Final)",
                    "jurisdictions": ["US"],
                    "sectors": ["power"]
                },
                "content": {
                    "summary": TEST_AI_SUMMARY,
                    "key_points": TEST_AI_KEY_POINTS
                },
                "details": None
            })
            
            # Test
            summary = service.process_unprocessed_articles()
            
            # Assertions
            assert summary["total_unprocessed"] == 2
            assert summary["processed_count"] == 2
            assert summary["failed_count"] == 0
            assert "timestamp" in summary
            
            # Verify articles are marked as processed
            db_session.refresh(article1)
            db_session.refresh(article2)
            assert article1.is_processed == True
            assert article2.is_processed == True
    
    def test_process_unprocessed_articles_with_failures(self, db_session):
        """Test processing with some failures."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            # Create unprocessed articles
            article1 = NewsArticle(
                title="Article 1",
                url="https://example.com/article1",
                content="Content 1",
                source_name="Source 1",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            article2 = NewsArticle(
                title="Article 2",
                url="https://example.com/article2",
                content="Content 2",
                source_name="Source 2",
                collected_at=datetime.now(timezone.utc),
                is_processed=False
            )
            
            db_session.add_all([article1, article2])
            db_session.commit()
            
            service = AINewsParsingService(db_session)
            
            # Mock parse_article to fail for second article
            def mock_parse_article(article):
                if article.id == article2.id:
                    raise Exception("Parsing failed")
                return {
                    "classification": TEST_AI_CLASSIFICATION,
                    "content": {"summary": TEST_AI_SUMMARY, "key_points": TEST_AI_KEY_POINTS}
                }
            
            service.parse_article = Mock(side_effect=mock_parse_article)
            
            # Test
            summary = service.process_unprocessed_articles()
            
            # Assertions
            assert summary["total_unprocessed"] == 2
            assert summary["processed_count"] == 1
            assert summary["failed_count"] == 1
    
    def test_generate_daily_summary_empty(self, db_session):
        """Test daily summary generation with no articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Test with empty list
            summary = service.generate_daily_summary([])
            
            # Assertions
            assert summary["summary"] == "No articles processed today."
            assert summary["key_developments"] == []
            assert summary["statistics"]["total_articles"] == 0
            assert "generated_at" in summary
    
    def test_generate_daily_summary_with_articles(self, db_session, processed_news_article):
        """Test daily summary generation with processed articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            
            # Mock AI summary response
            mock_result = Mock()
            from app.models import DailySummary
            mock_result.output = DailySummary(
                executive_summary="Daily summary of carbon regulation news",
                key_developments=["Development 1", "Development 2"],
                regulatory_changes=["Change 1"],
                market_implications=["Implication 1"],
                important_dates=["Date 1"]
            )
            mock_agent_instance.run_sync.return_value = mock_result
            
            service = AINewsParsingService(db_session)
            service._agents['summary'] = mock_agent_instance
            
            # Test with enhanced mode (default)
            summary = service.generate_daily_summary([processed_news_article])

            # Assertions for enhanced mode
            assert "markdown_summary" in summary  # Enhanced mode includes markdown
            assert "clusters" in summary  # Enhanced mode includes clusters
            assert len(summary["key_developments"]) >= 0  # May be extracted from markdown
            assert summary["statistics"]["total_articles"] == 1
            assert summary["statistics"]["processed_articles"] == 1
            assert "categories" in summary["statistics"]
            assert "generated_at" in summary

            # Test legacy mode explicitly
            legacy_summary = service.generate_daily_summary([processed_news_article], use_enhanced=False)

            # Assertions for legacy mode
            assert legacy_summary["executive_summary"] == "Daily summary of carbon regulation news"
            assert len(legacy_summary["key_developments"]) == 2
            assert legacy_summary["statistics"]["total_articles"] == 1
    
    def test_generate_daily_summary_ai_failure(self, db_session, processed_news_article):
        """Test daily summary generation when AI fails."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:
            
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance
            mock_agent_instance.run_sync.side_effect = Exception("AI Error")
            
            service = AINewsParsingService(db_session)
            service._agents['summary'] = mock_agent_instance
            
            # Test enhanced mode (default) - should use fallback markdown generation
            summary = service.generate_daily_summary([processed_news_article])

            # Should still generate summary with statistics
            assert summary["statistics"]["total_articles"] == 1
            # Enhanced mode provides fallback markdown summary instead of error message
            assert "markdown_summary" in summary
            assert "# Daily Carbon Regulation News Summary" in summary["markdown_summary"]
            assert summary["executive_summary"] != "Failed to generate AI summary"  # Should have fallback text
            assert "generated_at" in summary

            # Test legacy mode - should show AI failure
            legacy_summary = service.generate_daily_summary([processed_news_article], use_enhanced=False)
            assert legacy_summary["statistics"]["total_articles"] == 1
            assert legacy_summary["executive_summary"] == "Failed to generate AI summary"
            assert "generated_at" in legacy_summary
    
    def test_service_cleanup(self, db_session):
        """Test service cleanup on deletion."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model
            
            service = AINewsParsingService(db_session)
            
            # Mock the database session close method
            service.db.close = Mock()
            
            # Delete service
            del service
            
            # Note: __del__ is not guaranteed to be called immediately

    def test_cluster_articles_single_article(self, db_session):
        """Test article clustering with a single article."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            service = AINewsParsingService(db_session)

            article_summaries = [{
                "title": "Test Article",
                "summary": "Test summary",
                "category": "Carbon pricing & markets",
                "type": "Regulatory Update",
                "jurisdictions": ["US"],
                "key_points": ["Point 1"],
                "source": "Test Source",
                "url": "http://test.com"
            }]

            result = service._cluster_articles(article_summaries)

            # Should create a single cluster for one article
            assert len(result.clusters) == 1
            assert result.clusters[0].cluster_id == "single_article"
            assert result.clusters[0].theme == "Daily Update"
            assert result.clusters[0].article_indices == [0]

    def test_cluster_articles_multiple_articles(self, db_session):
        """Test article clustering with multiple articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:

            mock_model = Mock()
            mock_get_model.return_value = mock_model

            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance

            # Mock clustering response
            mock_result = Mock()
            import json
            mock_result.output = json.dumps({
                "clusters": [
                    {
                        "cluster_id": "carbon_pricing",
                        "theme": "Carbon Pricing Updates",
                        "description": "Articles about carbon pricing mechanisms",
                        "article_indices": [0, 1],
                        "priority": "High"
                    },
                    {
                        "cluster_id": "renewable_energy",
                        "theme": "Renewable Energy Policy",
                        "description": "Articles about renewable energy regulations",
                        "article_indices": [2],
                        "priority": "Medium"
                    }
                ]
            })
            mock_agent_instance.run_sync.return_value = mock_result

            service = AINewsParsingService(db_session)
            service._agents['clustering'] = mock_agent_instance

            article_summaries = [
                {"title": "Carbon Tax Update", "category": "Carbon pricing & markets"},
                {"title": "ETS Reform", "category": "Carbon pricing & markets"},
                {"title": "Solar Incentives", "category": "Energy transition"}
            ]

            result = service._cluster_articles(article_summaries)

            # Assertions
            assert len(result.clusters) == 2
            # Check that we have clusters for different categories (fallback creates category-based clusters)
            cluster_themes = [cluster.theme for cluster in result.clusters]
            assert "Carbon pricing & markets" in cluster_themes
            assert "Energy transition" in cluster_themes

    def test_cluster_articles_fallback(self, db_session):
        """Test article clustering fallback when AI fails."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:

            mock_model = Mock()
            mock_get_model.return_value = mock_model

            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance

            # Mock AI failure
            mock_agent_instance.run_sync.side_effect = Exception("AI clustering failed")

            service = AINewsParsingService(db_session)
            service._agents['clustering'] = mock_agent_instance

            article_summaries = [
                {"title": "Article 1", "category": "Carbon pricing & markets"},
                {"title": "Article 2", "category": "Energy transition"},
                {"title": "Article 3", "category": "Carbon pricing & markets"}
            ]

            result = service._cluster_articles(article_summaries)

            # Should fallback to category-based clustering
            assert len(result.clusters) == 2  # Two categories

            # Find clusters by category
            carbon_cluster = next(c for c in result.clusters if "carbon_pricing" in c.cluster_id)
            energy_cluster = next(c for c in result.clusters if "energy_transition" in c.cluster_id)

            assert len(carbon_cluster.article_indices) == 2  # Articles 0 and 2
            assert len(energy_cluster.article_indices) == 1   # Article 1

    def test_generate_enhanced_daily_summary_empty(self, db_session):
        """Test enhanced daily summary generation with no articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            service = AINewsParsingService(db_session)

            # Test with empty list
            summary = service.generate_enhanced_daily_summary([])

            # Assertions
            assert "markdown_summary" in summary
            assert "No articles processed today" in summary["markdown_summary"]
            assert summary["statistics"]["total_articles"] == 0
            assert summary["clusters"] == []
            assert "generated_at" in summary

    def test_generate_enhanced_daily_summary_with_articles(self, db_session, processed_news_article):
        """Test enhanced daily summary generation with processed articles."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model, \
             patch('app.services.ai_parser.Agent') as mock_agent:

            mock_model = Mock()
            mock_get_model.return_value = mock_model

            mock_agent_instance = Mock()
            mock_agent.return_value = mock_agent_instance

            # Mock clustering response
            clustering_result = Mock()
            clustering_result.output = json.dumps({
                "clusters": [{
                    "cluster_id": "test_cluster",
                    "theme": "Test Theme",
                    "description": "Test cluster description",
                    "article_indices": [0],
                    "priority": "High"
                }]
            })

            # Mock enhanced summary response
            summary_result = Mock()
            summary_result.output = """# Daily Carbon Regulation News Summary
*Test Date*

## Executive Summary

Today's developments include [key regulatory update](http://test.com) from Test Source.

## Key Developments by Theme

### Test Theme
- **[Test Article](http://test.com)** (Test Source)
  Test summary content with important details.

## Regional Focus
- **US**: 1 development

---
*Summary generated automatically from carbon regulation and climate policy news sources.*"""

            # Set up separate agent mocks for clustering and enhanced summary
            clustering_agent = Mock()
            clustering_agent.run_sync.return_value = clustering_result

            summary_agent = Mock()
            summary_agent.run_sync.return_value = summary_result

            service = AINewsParsingService(db_session)
            service._agents['clustering'] = clustering_agent
            service._agents['enhanced_summary'] = summary_agent

            # Test
            summary = service.generate_enhanced_daily_summary([processed_news_article])

            # Assertions
            assert "markdown_summary" in summary
            assert "# Daily Carbon Regulation News Summary" in summary["markdown_summary"]
            assert "clusters" in summary
            assert len(summary["clusters"]) == 1
            # With single article, it defaults to "single_article" cluster
            assert summary["clusters"][0]["cluster_id"] == "single_article"
            assert summary["statistics"]["total_articles"] == 1
            assert summary["statistics"]["cluster_count"] == 1
            assert "generated_at" in summary

    def test_extract_key_developments_from_markdown(self, db_session):
        """Test extraction of key developments from markdown summary."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            service = AINewsParsingService(db_session)

            markdown_text = """# Daily Summary

## Key Developments

- **Important regulatory update** with [source link](http://test.com)
- New carbon pricing mechanism announced
- *Climate policy* changes in the EU

## Other Section

1. First numbered item with details
2. Second numbered item about emissions

Some paragraph text that should be ignored.
"""

            key_developments = service._extract_key_developments_from_markdown(markdown_text)

            # Should extract bullet points and numbered items, clean formatting
            assert len(key_developments) >= 4
            assert "Important regulatory update with source link" in key_developments
            assert "New carbon pricing mechanism announced" in key_developments
            assert "Climate policy changes in the EU" in key_developments
            assert "First numbered item with details" in key_developments

    def test_extract_executive_summary_from_markdown(self, db_session):
        """Test extraction of executive summary from markdown."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            service = AINewsParsingService(db_session)

            markdown_text = """# Daily Carbon Regulation News Summary

## Executive Summary

Today's carbon regulation landscape features **significant developments** across multiple jurisdictions.
The [EU announced new policies](http://eu.com) while the US introduced *updated standards*.

Key regulatory changes include enhanced reporting requirements.

## Key Developments

- Other content here
"""

            exec_summary = service._extract_executive_summary_from_markdown(markdown_text)

            # Should extract and clean the executive summary section
            assert "Today's carbon regulation landscape features significant developments" in exec_summary
            assert "EU announced new policies" in exec_summary
            assert "updated standards" in exec_summary
            assert "Key regulatory changes include enhanced reporting requirements" in exec_summary
            # Should remove markdown formatting
            assert "**" not in exec_summary
            assert "[" not in exec_summary
            assert "*" not in exec_summary

    def test_generate_fallback_markdown_summary(self, db_session):
        """Test fallback markdown summary generation."""
        with patch('app.services.ai_parser.ModelManager.get_model') as mock_get_model:
            mock_model = Mock()
            mock_get_model.return_value = mock_model

            service = AINewsParsingService(db_session)

            clustered_articles = [{
                "cluster_id": "carbon_pricing",
                "theme": "Carbon Pricing Updates",
                "description": "Articles about carbon pricing mechanisms",
                "priority": "High",
                "articles": [{
                    "title": "New Carbon Tax Announced",
                    "summary": "Government announces new carbon tax policy",
                    "source": "Reuters",
                    "url": "http://reuters.com/carbon-tax"
                }]
            }]

            categories = {"Carbon pricing & markets": 1}
            jurisdictions = {"US": 1}

            markdown = service._generate_fallback_markdown_summary(clustered_articles, categories, jurisdictions)

            # Should generate proper markdown structure
            assert "# Daily Carbon Regulation News Summary" in markdown
            assert "## Executive Summary" in markdown
            assert "## Key Developments by Theme" in markdown
            assert "### Carbon Pricing Updates" in markdown
            assert "## Regional Focus" in markdown
            assert "**US**: 1 development" in markdown
            assert "[New Carbon Tax Announced](http://reuters.com/carbon-tax)" in markdown
            assert "(Reuters)" in markdown
